const { Client } = require('pg');
require('dotenv').config();

async function testConnection() {
  // Try different password combinations
  const configs = [
    {
      host: 'localhost',
      port: 5432,
      user: 'thoughtsync',
      password: 'thoughtsync_password',
      database: 'thoughtsync_db',
    },
    {
      host: 'localhost',
      port: 5432,
      user: 'thoughtsync',
      password: '',
      database: 'thoughtsync_db',
    },
    {
      host: 'localhost',
      port: 5432,
      user: 'postgres',
      password: 'thoughtsync_password',
      database: 'thoughtsync_db',
    }
  ];

  for (const config of configs) {
    const client = new Client(config);
    try {
      console.log(`Trying config: ${config.user}@${config.host}:${config.port}/${config.database}`);
      await client.connect();
      console.log('✅ Database connection successful with config:', config);

      const result = await client.query('SELECT NOW()');
      console.log('✅ Query successful:', result.rows[0]);

      await client.end();
      return; // Success, exit
    } catch (error) {
      console.log(`❌ Failed with config ${config.user}: ${error.message}`);
      try { await client.end(); } catch {}
    }
  }

  try {
    console.log('Attempting to connect to database...');
    await client.connect();
    console.log('✅ Database connection successful!');
    
    const result = await client.query('SELECT NOW()');
    console.log('✅ Query successful:', result.rows[0]);
    
    await client.end();
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Error details:', error);
  }
}

testConnection();
