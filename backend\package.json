{"name": "synergyai-backend", "version": "1.0.0", "description": "SynergyAI Backend API", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/simple-server.ts", "build": "tsc", "start": "node dist/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx src/scripts/seed.ts", "db:clear-demo": "tsx src/scripts/clear-demo-data.ts", "db:clear-all": "tsx src/scripts/clear-demo-data.ts --all", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@google/generative-ai": "^0.2.1", "@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "langchain": "^0.1.0", "morgan": "^1.10.0", "openai": "^4.24.1", "pg": "^8.16.3", "pinecone-client": "^1.1.0", "redis": "^4.6.12", "socket.io": "^4.7.4", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^5.7.1", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "keywords": ["synergyai", "ai", "workflow", "collaboration", "api"], "author": "SynergyAI Team", "license": "MIT"}