import { Router } from 'express';
import { prisma } from '../lib/prisma';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { validateBody, validateParams } from '../middleware/validation';
import { createError } from '../middleware/errorHandler';
import {
  createWorkspaceSchema,
  updateWorkspaceSchema,
  addMemberSchema,
  updateMemberRoleSchema,
  workspaceParamsSchema,
  memberParamsSchema,
  CreateWorkspaceInput,
  UpdateWorkspaceInput,
  AddMemberInput,
  UpdateMemberRoleInput
} from '../schemas/workspace';

const router = Router();

// All workspace routes require authentication
router.use(authenticateToken);

// Helper function to check workspace access
const checkWorkspaceAccess = async (workspaceId: string, userId: string, requiredRole?: string) => {
  const workspace = await prisma.workspace.findUnique({
    where: { id: workspaceId },
    include: {
      members: {
        where: { userId },
        select: { role: true }
      },
      owner: {
        select: { id: true }
      }
    }
  });

  if (!workspace) {
    throw createError('Workspace not found', 404);
  }

  const isOwner = workspace.owner.id === userId;
  const member = workspace.members[0];
  
  if (!isOwner && !member) {
    throw createError('Access denied', 403);
  }

  const userRole = isOwner ? 'owner' : member.role;

  if (requiredRole) {
    const roleHierarchy = ['viewer', 'member', 'admin', 'owner'];
    const userRoleIndex = roleHierarchy.indexOf(userRole);
    const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);

    if (userRoleIndex < requiredRoleIndex) {
      throw createError('Insufficient permissions', 403);
    }
  }

  return { workspace, userRole };
};

// Get user's workspaces
router.get('/', async (req: AuthenticatedRequest, res, next) => {
  try {
    const workspaces = await prisma.workspace.findMany({
      where: {
        OR: [
          { ownerId: req.user!.id },
          { members: { some: { userId: req.user!.id } } }
        ]
      },
      include: {
        owner: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        members: {
          include: {
            user: {
              select: { id: true, username: true, firstName: true, lastName: true }
            }
          }
        },
        _count: {
          select: { projects: true }
        }
      },
      orderBy: { updatedAt: 'desc' }
    });

    // Add user role to each workspace
    const workspacesWithRole = workspaces.map(workspace => {
      const isOwner = workspace.ownerId === req.user!.id;
      const member = workspace.members.find(m => m.userId === req.user!.id);
      const userRole = isOwner ? 'owner' : member?.role || 'viewer';

      return {
        ...workspace,
        userRole
      };
    });

    res.json({ workspaces: workspacesWithRole });
  } catch (error) {
    next(error);
  }
});

// Create workspace
router.post('/', validateBody(createWorkspaceSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { name, description, settings }: CreateWorkspaceInput = req.body;

    const workspace = await prisma.workspace.create({
      data: {
        name,
        description,
        settings: settings || {},
        ownerId: req.user!.id,
      },
      include: {
        owner: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        _count: {
          select: { projects: true, members: true }
        }
      }
    });

    res.status(201).json({
      message: 'Workspace created successfully',
      workspace: {
        ...workspace,
        userRole: 'owner'
      }
    });
  } catch (error) {
    next(error);
  }
});

// Get workspace details
router.get('/:id', validateParams(workspaceParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { workspace, userRole } = await checkWorkspaceAccess(id, req.user!.id);

    const workspaceDetails = await prisma.workspace.findUnique({
      where: { id },
      include: {
        owner: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        members: {
          include: {
            user: {
              select: { id: true, username: true, firstName: true, lastName: true, email: true }
            }
          },
          orderBy: { joinedAt: 'asc' }
        },
        projects: {
          select: {
            id: true,
            title: true,
            description: true,
            status: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: { conversations: true, goals: true, milestones: true }
            }
          },
          orderBy: { updatedAt: 'desc' }
        }
      }
    });

    res.json({
      workspace: {
        ...workspaceDetails,
        userRole
      }
    });
  } catch (error) {
    next(error);
  }
});

// Update workspace
router.put('/:id', validateParams(workspaceParamsSchema), validateBody(updateWorkspaceSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { name, description, settings }: UpdateWorkspaceInput = req.body;

    await checkWorkspaceAccess(id, req.user!.id, 'admin');

    const workspace = await prisma.workspace.update({
      where: { id },
      data: {
        ...(name !== undefined && { name }),
        ...(description !== undefined && { description }),
        ...(settings !== undefined && { settings }),
      },
      include: {
        owner: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        _count: {
          select: { projects: true, members: true }
        }
      }
    });

    res.json({
      message: 'Workspace updated successfully',
      workspace
    });
  } catch (error) {
    next(error);
  }
});

// Delete workspace
router.delete('/:id', validateParams(workspaceParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    // Only owner can delete workspace
    const workspace = await prisma.workspace.findUnique({
      where: { id },
      select: { ownerId: true }
    });

    if (!workspace) {
      throw createError('Workspace not found', 404);
    }

    if (workspace.ownerId !== req.user!.id) {
      throw createError('Only workspace owner can delete workspace', 403);
    }

    await prisma.workspace.delete({
      where: { id }
    });

    res.json({ message: 'Workspace deleted successfully' });
  } catch (error) {
    next(error);
  }
});

// Get workspace members
router.get('/:id/members', validateParams(workspaceParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    await checkWorkspaceAccess(id, req.user!.id);

    const members = await prisma.workspaceMember.findMany({
      where: { workspaceId: id },
      include: {
        user: {
          select: { id: true, username: true, firstName: true, lastName: true, email: true }
        }
      },
      orderBy: { joinedAt: 'asc' }
    });

    // Also include the owner
    const workspace = await prisma.workspace.findUnique({
      where: { id },
      include: {
        owner: {
          select: { id: true, username: true, firstName: true, lastName: true, email: true }
        }
      }
    });

    const allMembers = [
      {
        user: workspace!.owner,
        role: 'owner',
        joinedAt: workspace!.createdAt,
        workspaceId: id,
        userId: workspace!.owner.id
      },
      ...members
    ];

    res.json({ members: allMembers });
  } catch (error) {
    next(error);
  }
});

// Add member to workspace
router.post('/:id/members', validateParams(workspaceParamsSchema), validateBody(addMemberSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { email, role }: AddMemberInput = req.body;

    await checkWorkspaceAccess(id, req.user!.id, 'admin');

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, username: true, firstName: true, lastName: true, email: true }
    });

    if (!user) {
      throw createError('User not found', 404);
    }

    // Check if user is already a member
    const existingMember = await prisma.workspaceMember.findUnique({
      where: {
        workspaceId_userId: {
          workspaceId: id,
          userId: user.id
        }
      }
    });

    if (existingMember) {
      throw createError('User is already a member of this workspace', 409);
    }

    // Check if user is the owner
    const workspace = await prisma.workspace.findUnique({
      where: { id },
      select: { ownerId: true }
    });

    if (workspace!.ownerId === user.id) {
      throw createError('User is already the owner of this workspace', 409);
    }

    const member = await prisma.workspaceMember.create({
      data: {
        workspaceId: id,
        userId: user.id,
        role,
      },
      include: {
        user: {
          select: { id: true, username: true, firstName: true, lastName: true, email: true }
        }
      }
    });

    res.status(201).json({
      message: 'Member added successfully',
      member
    });
  } catch (error) {
    next(error);
  }
});

// Update member role
router.put('/:workspaceId/members/:userId', validateParams(memberParamsSchema), validateBody(updateMemberRoleSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { workspaceId, userId } = req.params;
    const { role }: UpdateMemberRoleInput = req.body;

    await checkWorkspaceAccess(workspaceId, req.user!.id, 'admin');

    const member = await prisma.workspaceMember.update({
      where: {
        workspaceId_userId: {
          workspaceId,
          userId
        }
      },
      data: { role },
      include: {
        user: {
          select: { id: true, username: true, firstName: true, lastName: true, email: true }
        }
      }
    });

    res.json({
      message: 'Member role updated successfully',
      member
    });
  } catch (error) {
    next(error);
  }
});

// Remove member from workspace
router.delete('/:workspaceId/members/:userId', validateParams(memberParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { workspaceId, userId } = req.params;

    await checkWorkspaceAccess(workspaceId, req.user!.id, 'admin');

    // Cannot remove the owner
    const workspace = await prisma.workspace.findUnique({
      where: { id: workspaceId },
      select: { ownerId: true }
    });

    if (workspace!.ownerId === userId) {
      throw createError('Cannot remove workspace owner', 400);
    }

    await prisma.workspaceMember.delete({
      where: {
        workspaceId_userId: {
          workspaceId,
          userId
        }
      }
    });

    res.json({ message: 'Member removed successfully' });
  } catch (error) {
    next(error);
  }
});

export default router;