#!/bin/bash

# Rebuild Docker containers with updated code
echo "🔨 Rebuilding SynergyAI Docker Containers with Latest Changes"
echo "============================================================"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

echo "🛑 Stopping existing containers..."
docker-compose down
echo "✅ Containers stopped"
echo ""

echo "🧹 Removing old images to force rebuild..."
docker rmi thoughtsync-frontend thoughtsync-backend 2>/dev/null || true
echo "✅ Old images removed"
echo ""

echo "🔨 Building containers with latest code (no cache)..."
docker-compose build --no-cache
echo "✅ Containers rebuilt"
echo ""

echo "🚀 Starting updated containers..."
docker-compose up -d
echo "✅ Containers started"
echo ""

echo "⏳ Waiting for services to be ready..."
sleep 15

echo "📊 Container Status:"
docker-compose ps
echo ""

echo "🌐 Application URLs:"
echo "Frontend: http://localhost:3000"
echo "Backend:  http://localhost:3001/health"
echo ""

echo "🎉 Rebuild completed!"
echo ""
echo "📋 Next steps:"
echo "1. Open http://localhost:3000 in your browser"
echo "2. Hard refresh (Ctrl+F5 or Cmd+Shift+R) to clear browser cache"
echo "3. You should now see the updated UI without errors"
echo ""
