import { Router } from 'express';
import { prisma } from '../lib/prisma';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { validateBody, validateParams } from '../middleware/validation';
import { createError } from '../middleware/errorHandler';
import {
  createProjectSchema,
  updateProjectSchema,
  createGoalSchema,
  updateGoalSchema,
  createMilestoneSchema,
  updateMilestoneSchema,
  projectParamsSchema,
  goalParamsSchema,
  milestoneParamsSchema,
  workspaceProjectParamsSchema,
  CreateProjectInput,
  UpdateProjectInput,
  CreateGoalInput,
  UpdateGoalInput,
  CreateMilestoneInput,
  UpdateMilestoneInput
} from '../schemas/project';

const router = Router();

// All project routes require authentication
router.use(authenticateToken);

// Helper function to check project access
const checkProjectAccess = async (projectId: string, userId: string) => {
  const project = await prisma.project.findUnique({
    where: { id: projectId },
    include: {
      workspace: {
        include: {
          members: {
            where: { userId },
            select: { role: true }
          },
          owner: {
            select: { id: true }
          }
        }
      }
    }
  });

  if (!project) {
    throw createError('Project not found', 404);
  }

  const isOwner = project.workspace.owner.id === userId;
  const member = project.workspace.members[0];
  
  if (!isOwner && !member) {
    throw createError('Access denied', 403);
  }

  return project;
};

// Helper function to check workspace access for project creation
const checkWorkspaceAccess = async (workspaceId: string, userId: string) => {
  const workspace = await prisma.workspace.findUnique({
    where: { id: workspaceId },
    include: {
      members: {
        where: { userId },
        select: { role: true }
      },
      owner: {
        select: { id: true }
      }
    }
  });

  if (!workspace) {
    throw createError('Workspace not found', 404);
  }

  const isOwner = workspace.owner.id === userId;
  const member = workspace.members[0];
  
  if (!isOwner && !member) {
    throw createError('Access denied', 403);
  }

  // Check if user can create projects (member role or higher)
  const userRole = isOwner ? 'owner' : member.role;
  if (userRole === 'viewer') {
    throw createError('Insufficient permissions to create projects', 403);
  }

  return workspace;
};

// Get projects in workspace
router.get('/workspace/:workspaceId', validateParams(workspaceProjectParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { workspaceId } = req.params;
    
    await checkWorkspaceAccess(workspaceId, req.user!.id);

    const projects = await prisma.project.findMany({
      where: { workspaceId },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        _count: {
          select: { 
            conversations: true, 
            goals: true, 
            milestones: true 
          }
        }
      },
      orderBy: { updatedAt: 'desc' }
    });

    res.json({ projects });
  } catch (error) {
    next(error);
  }
});

// Create project
router.post('/workspace/:workspaceId', validateParams(workspaceProjectParamsSchema), validateBody(createProjectSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { workspaceId } = req.params;
    const { title, description, scope, assumptions, constraints, successCriteria }: CreateProjectInput = req.body;

    await checkWorkspaceAccess(workspaceId, req.user!.id);

    const project = await prisma.project.create({
      data: {
        workspaceId,
        title,
        description,
        scope,
        assumptions,
        constraints,
        successCriteria,
        createdBy: req.user!.id,
      },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        workspace: {
          select: { id: true, name: true }
        },
        _count: {
          select: { 
            conversations: true, 
            goals: true, 
            milestones: true 
          }
        }
      }
    });

    res.status(201).json({
      message: 'Project created successfully',
      project
    });
  } catch (error) {
    next(error);
  }
});

// Get project details
router.get('/:id', validateParams(projectParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    
    await checkProjectAccess(id, req.user!.id);

    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        workspace: {
          select: { id: true, name: true }
        },
        conversations: {
          select: {
            id: true,
            name: true,
            parentId: true,
            isContextShielded: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: { messages: true }
            }
          },
          orderBy: { updatedAt: 'desc' }
        },
        goals: {
          include: {
            creator: {
              select: { id: true, username: true }
            }
          },
          orderBy: { createdAt: 'desc' }
        },
        milestones: {
          include: {
            creator: {
              select: { id: true, username: true }
            }
          },
          orderBy: { targetDate: 'asc' }
        },
        iterationCycles: {
          include: {
            creator: {
              select: { id: true, username: true }
            }
          },
          orderBy: { cycleNumber: 'desc' }
        }
      }
    });

    res.json({ project });
  } catch (error) {
    next(error);
  }
});

// Update project
router.put('/:id', validateParams(projectParamsSchema), validateBody(updateProjectSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const updateData: UpdateProjectInput = req.body;

    await checkProjectAccess(id, req.user!.id);

    const project = await prisma.project.update({
      where: { id },
      data: updateData,
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        },
        workspace: {
          select: { id: true, name: true }
        },
        _count: {
          select: { 
            conversations: true, 
            goals: true, 
            milestones: true 
          }
        }
      }
    });

    res.json({
      message: 'Project updated successfully',
      project
    });
  } catch (error) {
    next(error);
  }
});

// Delete project
router.delete('/:id', validateParams(projectParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    
    await checkProjectAccess(id, req.user!.id);

    await prisma.project.delete({
      where: { id }
    });

    res.json({ message: 'Project deleted successfully' });
  } catch (error) {
    next(error);
  }
});

// Goals endpoints

// Get project goals
router.get('/:id/goals', validateParams(projectParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    
    await checkProjectAccess(id, req.user!.id);

    const goals = await prisma.goal.findMany({
      where: { projectId: id },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        }
      },
      orderBy: [
        { completed: 'asc' },
        { priority: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    res.json({ goals });
  } catch (error) {
    next(error);
  }
});

// Create goal
router.post('/:id/goals', validateParams(projectParamsSchema), validateBody(createGoalSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { text, type, priority }: CreateGoalInput = req.body;

    await checkProjectAccess(id, req.user!.id);

    const goal = await prisma.goal.create({
      data: {
        projectId: id,
        text,
        type,
        priority,
        createdBy: req.user!.id,
      },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        }
      }
    });

    res.status(201).json({
      message: 'Goal created successfully',
      goal
    });
  } catch (error) {
    next(error);
  }
});

// Update goal
router.put('/goals/:id', validateParams(goalParamsSchema), validateBody(updateGoalSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const updateData: UpdateGoalInput = req.body;

    // Check if goal exists and user has access to its project
    const goal = await prisma.goal.findUnique({
      where: { id },
      include: { project: true }
    });

    if (!goal) {
      throw createError('Goal not found', 404);
    }

    await checkProjectAccess(goal.projectId, req.user!.id);

    // If marking as completed, set completedAt
    if (updateData.completed === true && !goal.completed) {
      updateData.completedAt = new Date();
    } else if (updateData.completed === false) {
      updateData.completedAt = null;
    }

    const updatedGoal = await prisma.goal.update({
      where: { id },
      data: updateData,
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        }
      }
    });

    res.json({
      message: 'Goal updated successfully',
      goal: updatedGoal
    });
  } catch (error) {
    next(error);
  }
});

// Delete goal
router.delete('/goals/:id', validateParams(goalParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    const goal = await prisma.goal.findUnique({
      where: { id },
      include: { project: true }
    });

    if (!goal) {
      throw createError('Goal not found', 404);
    }

    await checkProjectAccess(goal.projectId, req.user!.id);

    await prisma.goal.delete({
      where: { id }
    });

    res.json({ message: 'Goal deleted successfully' });
  } catch (error) {
    next(error);
  }
});

// Milestones endpoints

// Get project milestones
router.get('/:id/milestones', validateParams(projectParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    
    await checkProjectAccess(id, req.user!.id);

    const milestones = await prisma.milestone.findMany({
      where: { projectId: id },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        }
      },
      orderBy: [
        { completed: 'asc' },
        { targetDate: 'asc' },
        { createdAt: 'desc' }
      ]
    });

    res.json({ milestones });
  } catch (error) {
    next(error);
  }
});

// Create milestone
router.post('/:id/milestones', validateParams(projectParamsSchema), validateBody(createMilestoneSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { title, description, targetDate }: CreateMilestoneInput = req.body;

    await checkProjectAccess(id, req.user!.id);

    const milestone = await prisma.milestone.create({
      data: {
        projectId: id,
        title,
        description,
        targetDate: targetDate ? new Date(targetDate) : null,
        createdBy: req.user!.id,
      },
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        }
      }
    });

    res.status(201).json({
      message: 'Milestone created successfully',
      milestone
    });
  } catch (error) {
    next(error);
  }
});

// Update milestone
router.put('/milestones/:id', validateParams(milestoneParamsSchema), validateBody(updateMilestoneSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const updateData: UpdateMilestoneInput = req.body;

    const milestone = await prisma.milestone.findUnique({
      where: { id },
      include: { project: true }
    });

    if (!milestone) {
      throw createError('Milestone not found', 404);
    }

    await checkProjectAccess(milestone.projectId, req.user!.id);

    // If marking as completed, set completedAt
    if (updateData.completed === true && !milestone.completed) {
      updateData.completedAt = new Date();
    } else if (updateData.completed === false) {
      updateData.completedAt = null;
    }

    // Handle targetDate conversion
    const finalUpdateData = {
      ...updateData,
      ...(updateData.targetDate && { targetDate: new Date(updateData.targetDate) })
    };

    const updatedMilestone = await prisma.milestone.update({
      where: { id },
      data: finalUpdateData,
      include: {
        creator: {
          select: { id: true, username: true, firstName: true, lastName: true }
        }
      }
    });

    res.json({
      message: 'Milestone updated successfully',
      milestone: updatedMilestone
    });
  } catch (error) {
    next(error);
  }
});

// Delete milestone
router.delete('/milestones/:id', validateParams(milestoneParamsSchema), async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    const milestone = await prisma.milestone.findUnique({
      where: { id },
      include: { project: true }
    });

    if (!milestone) {
      throw createError('Milestone not found', 404);
    }

    await checkProjectAccess(milestone.projectId, req.user!.id);

    await prisma.milestone.delete({
      where: { id }
    });

    res.json({ message: 'Milestone deleted successfully' });
  } catch (error) {
    next(error);
  }
});

export default router;